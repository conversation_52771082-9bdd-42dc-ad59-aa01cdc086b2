import React from "react";
import Head from "next/head";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import { dateFormateWithTimeShort, htmlParser } from "@/utils/Util";
import { usePathname } from "next/navigation";
import { Const } from "@/utils/Constants";
import { useRouter } from "next/router";

export const config = { amp: true };

const WebStoryDetail = ({ meta, data, previousData, nextData, breadcrumbs, tag }) => {
	const pathname = usePathname();
	const router = useRouter();
	console.log("data on amp webstory page", data);
	const css = `
		.story-navigation {
			position: fixed;
			top: 50%;
			transform: translateY(-50%);
			z-index: 1000;
			background: rgba(0,0,0,0.5);
			color: white;
			border: none;
			padding: 1rem;
			cursor: pointer;
			border-radius: 50%;
		}
		.story-navigation:disabled {
			opacity: 0.3;
			cursor: not-allowed;
		}
		.story-nav-prev {
			left: 20px;
		}
		.story-nav-next {
			right: 20px;
		}
		.brand-logo {
			position: absolute;
			top: 20px;
			left: 20px;
			z-index: 100;
			color: white;
			font-weight: bold;
			font-size: 1.2rem;
		}
		.next-story-link {
			position: absolute;
			bottom: 20px;
			left: 50%;
			transform: translateX(-50%);
			z-index: 100;
			background: rgba(255,255,255,0.9);
			padding: 0.5rem 1rem;
			border-radius: 20px;
			text-decoration: none;
			color: #000;
			font-weight: bold;
		}
	`;

	return (
		<>
			<style jsx>{css}</style>

			<Head>
				<script
					async
					custom-element="amp-story"
					src="https://cdn.ampproject.org/v0/amp-story-1.0.js"
				></script>
			</Head>
			<SeoHeader meta={meta} />
			<BreadcrumbSchema itemList={breadcrumbs} />

			{/* Navigation Buttons */}
			{previousData && (
				<button
					className="story-navigation story-nav-prev"
					onClick={() => router.push(previousData.link)}
				>
					←
				</button>
			)}
			{nextData && (
				<button
					className="story-navigation story-nav-next"
					onClick={() => router.push(nextData.link)}
				>
					→
				</button>
			)}

			<amp-story
				standalone
				title={data?.slides?.[0]?.title || ""}
				publisher="Manifest"
				publisher-logo-src="/logo svg.svg"
				poster-portrait-src={data?.slides?.[0]?.image || ""}
			>
				{data.slides.map((slide, index) => (
					<amp-story-page key={index} id={`page-${index}`}>
						<amp-story-grid-layer template="fill">
							<amp-img
								src={slide.image}
								width="720"
								height="1280"
								layout="responsive"
								alt={slide.title}
							></amp-img>
						</amp-story-grid-layer>

						<amp-story-grid-layer template="vertical">
							{/* Brand logo on first slide */}
							{index === 0 && <div className="brand-logo">Manifest</div>}

							{/* Content */}
							<h1
								style={{
									color: "white",
									padding: "0 20px",
									textAlign: "center",
									marginTop: "auto",
									background: "rgba(0,0,0,0.5)",
								}}
							>
								{slide.title}
							</h1>

							<div
								style={{
									color: "white",
									padding: "0 20px 20px",
									textAlign: "center",
									background: "rgba(0,0,0,0.5)",
								}}
								dangerouslySetInnerHTML={{ __html: slide.description }}
							/>

							{/* Next story link on last slide */}
							{index === data.slides.length - 1 && nextData && (
								<a href={nextData.link} className="next-story-link">
									Next Story →
								</a>
							)}

							{/* Credits */}
							{slide.contributor?.length > 0 && (
								<small
									style={{
										color: "white",
										padding: "5px 20px",
										background: "rgba(0,0,0,0.3)",
									}}
								>
									Photo Credit: {slide.contributor.join(", ")}
								</small>
							)}
						</amp-story-grid-layer>
					</amp-story-page>
				))}
			</amp-story>
		</>
	);
};

export default WebStoryDetail;

export async function getServerSideProps(context) {
	const { subcategory, stories } = context.query;

	console.log("WebStory getServerSideProps - subcategory:", subcategory);
	console.log("WebStory getServerSideProps - stories:", stories);

	try {
		const { getWebStories } = await import("@/pages/api/WebStoriesApi");
		// Construct the full slug path for the API call
		const fullSlug = `/webstories/${subcategory}/${stories}`;
		console.log("WebStory getServerSideProps - fullSlug:", fullSlug);

		const response = await getWebStories(fullSlug);
		console.log("WebStory getServerSideProps - response:", response);

		if (!response || !response.success) {
			console.log("WebStory getServerSideProps - API response failed or no success flag");
			return { notFound: true };
		}

		return {
			props: {
				meta: response.meta || {},
				data: response.data || { slides: [] },
				previousData: response.previousData || null,
				nextData: response.nextData || null,
				breadcrumbs: response.breadcrumbs || [],
				tag: response.tag || null,
			},
		};
	} catch (error) {
		console.error("Error fetching web story:", error);
		return { notFound: true };
	}
}
