import React, { useState } from "react";
import { useRouter } from "next/router";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Hero from "@/components/category/Hero";
import TransparentSection from "@/components/common/TransparentSection";
import Webcard from "@/components/cards/Webcard";
import Button from "@/components/common/Button";
import { Const } from "@/utils/Constants";
import { getSubmenus } from "@/pages/api/CategoryApi";
import { getWebStoriesCategory } from "@/pages/api/WebStoriesApi";
import gsap from "gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import { useGSAP } from "@gsap/react";
gsap.registerPlugin(ScrollTrigger);
const WebStoryCategory = ({ meta, title, initialData, initialCount, breadcrumbs, submenus }) => {
	console.log("outer page for index");
	const router = useRouter();
	const [data, setData] = useState(initialData);
	const [totalCount, setTotalCount] = useState(initialCount);
	const [hasMore, setHasMore] = useState(initialData.length < initialCount);
	const [loading, setLoading] = useState(false);
	const [offset, setOffset] = useState(Const.Limit);

	const handleLoadMore = async () => {
		if (!hasMore || loading) return;

		setLoading(true);

		const payload = {
			slug: `/webstories/${router.query.subcategory}`,
			limit: Const.Limit,
			offset: offset,
		};

		try {
			const response = await getWebStoriesCategory(payload);
			const newData = response?.data?.data || [];
			const newTotalCount = response?.data?.count || 0;

			setData((prevData) => [...prevData, ...newData]);
			setTotalCount(newTotalCount);
			setOffset((prevOffset) => prevOffset + Const.Limit);

			if (data.length + newData.length >= newTotalCount) {
				setHasMore(false);
			}
		} catch (error) {
			console.error("Error loading more data:", error);
		} finally {
			setLoading(false);
		}
	};

	// Split data into columns for desktop view
	const columnCount = 3;
	const splitData = Array.from({ length: columnCount }, (_, colIndex) =>
		data.filter((_, itemIndex) => itemIndex % columnCount === colIndex)
	);
	useGSAP(() => {
		const scrollCntr = gsap.utils.toArray(".col");

		scrollCntr.forEach((col) => {
			gsap.to(col.querySelectorAll(".webstories_itemCard"), {
				y: () => (1 - parseFloat(col.getAttribute("data-speed"))) * window.innerHeight,
				scrollTrigger: {
					trigger: col,
					scrub: 0,
					start: "top top",
					end: () =>
						`bottom -=${(1 - parseFloat(col.getAttribute("data-speed"))) * window.innerHeight}px`,
					ease: "none",
				},
			});
		});
	});
	return (
		<>
			<SeoHeader meta={meta} />
			<BreadcrumbSchema itemList={breadcrumbs?.slice(0, 1) ?? []} />
			<div className="webstoriesPage_cntr">
				<div className="container">
					<Hero title={title} />
					{/* Desktop grid view */}
					<div className="webstories_grid_wrapper_desktop">
						{splitData.map((columnData, colIndex) => (
							<div
								className={`col_${colIndex + 1} col`}
								key={colIndex}
								data-scroll
								// data-scroll-speed={colIndex === 1 ? "0.8" : ""}
								data-speed={colIndex === 1 ? "3" : colIndex === 0 || colIndex === 2 ? "1" : "1"}
							>
								{columnData.map((el, index) => (
									<Webcard
										key={`${colIndex}-${index}`}
										title={el.title}
										image={el.coverImg}
										altName={el.altName}
										category={el.category}
										slug={el.slug}
										timestamp={el.timestamp}
										author={el.author}
										contributor={el.contributor}
									/>
								))}
							</div>
						))}
					</div>
					{/* Mobile grid view */}
					<div className="webstories_grid_wrapper_mbl">
						<div className="GridCardContainer">
							{data.map((el, index) => (
								<Webcard
									key={`mbl-${index}`}
									title={el.title}
									image={el.coverImg}
									altName={el.altName}
									category={el.category}
									slug={el.slug}
									timestamp={el.timestamp}
									author={el.author}
									contributor={el.contributor}
								/>
							))}
						</div>
					</div>
					{/* Load More Button */}
					{hasMore && (
						<div className="flex-all">
							<Button onClick={handleLoadMore} disabled={loading}>
								{loading ? "Loading..." : "SEE MORE"}
							</Button>
						</div>
					)}
				</div>
			</div>
			<TransparentSection />
		</>
	);
};

export default WebStoryCategory;

export async function getServerSideProps({ params }) {
	const slug = `/webstories/${params.subcategory}`;
	const payload = {
		slug,
		limit: Const.Limit,
		offset: Const.Offset,
	};

	try {
		const [categoryRes, submenusRes] = await Promise.all([
			getWebStoriesCategory(payload),
			getSubmenus(slug),
		]);

		if (!categoryRes?.data?.isExists) {
			return { notFound: true };
		}

		const categoryData = categoryRes?.data?.data || [];
		const categoryCount = categoryRes?.data?.count || 0;
		const breadcrumbs = categoryRes?.data?.breadcrumbs ?? [];
		const submenus = submenusRes?.data?.submenus || [];
		const title = submenusRes?.data?.title || "";
		const meta = categoryRes?.data?.meta || {};

		return {
			props: {
				initialData: categoryData,
				initialCount: categoryCount,
				breadcrumbs,
				submenus,
				meta,
				title,
			},
		};
	} catch (error) {
		console.error("Error fetching category data:", error.message);
		return { notFound: true };
	}
}
