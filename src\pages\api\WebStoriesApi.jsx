import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Article Data
export const getWebStories = async (slug) => {
	const apiUrl = Const.Link + `api/tenant/webstories?slug=${slug}`;
	console.log("getWebStories - API URL:", apiUrl);
	console.log("getWebStories - slug parameter:", slug);

	const res = await fetch(apiUrl, new Headers("GET"));
	console.log("getWebStories - response status:", res.status);

	const result = await ProcessAPI(res);
	console.log("getWebStories - processed result:", result);

	return result;
};

export const getWebStoriesCategory = async (body) => {
	const res = await fetch(
		Const.Link +
			`api/tenant/category-web-story?slug=${body.slug}&limit=${body.limit}&offset=${body.offset}`,
		new Headers("GET")
	);
	return ProcessAPI(res);
};

// Get Tag Sitemap
export const getWebstoriesSitemap = async (slug) => {
	const res = await fetch(Const.Link + "api/tenant/webstories-sitemap", new Headers("GET"));
	return ProcessAPI(res);
};
